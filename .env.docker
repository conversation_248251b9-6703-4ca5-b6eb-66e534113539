# Docker Compose Environment Variables
# Copy this file to .env and update the values as needed

# Database Configuration
POSTGRES_DB=atma_db
POSTGRES_USER=atma_user
POSTGRES_PASSWORD=atma_password

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=atma_user
RABBITMQ_DEFAULT_PASS=atma_password

# Security Keys (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=atma_super_secret_jwt_key_production_2024
INTERNAL_SERVICE_KEY=internal_service_secret_production_2024

# Google AI API Key (Required for analysis-worker)
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Environment
NODE_ENV=production

# Logging
LOG_LEVEL=info

version: '3.8'

services:
  # Database
  postgres:
    image: postgres:17-alpine
    container_name: atma-postgres
    environment:
      POSTGRES_DB: atma_db
      POSTGRES_USER: atma_user
      POSTGRES_PASSWORD: atma_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./current_database_dump.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U atma_user -d atma_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - atma-network

  # Message Queue
  rabbitmq:
    image: rabbitmq:4.1-management-alpine
    container_name: atma-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: atma_user
      RABBITMQ_DEFAULT_PASS: atma_password
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - atma-network

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: atma-auth-service
    environment:
      PORT: 3001
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: atma_db
      DB_USER: atma_user
      DB_PASSWORD: atma_password
      DB_DIALECT: postgres
      DB_SCHEMA: auth
      JWT_SECRET: atma_super_secret_jwt_key_production_2024
      JWT_EXPIRES_IN: 7d
      BCRYPT_ROUNDS: 10
      DEFAULT_TOKEN_BALANCE: 5
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
      LOG_LEVEL: info
      LOG_FILE: logs/auth-service.log
      ASYNC_LAST_LOGIN: true
      ENABLE_QUERY_CACHE: true
      ENABLE_PERFORMANCE_MONITORING: true
      DB_POOL_MAX: 25
      DB_POOL_MIN: 5
      DB_POOL_ACQUIRE: 60000
      DB_POOL_IDLE: 30000
      ENABLE_CACHE: false
      ENABLE_USER_CACHE: false
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./auth-service/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped

  # Archive Service
  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    container_name: atma-archive-service
    environment:
      PORT: 3002
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: atma_db
      DB_USER: atma_user
      DB_PASSWORD: atma_password
      DB_DIALECT: postgres
      DB_SCHEMA: archive
      JWT_SECRET: atma_super_secret_jwt_key_production_2024
      AUTH_SERVICE_URL: http://auth-service:3001
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
      DEFAULT_PAGE_SIZE: 10
      MAX_PAGE_SIZE: 100
      LOG_LEVEL: info
      LOG_FILE: logs/archive-service.log
      CORS_ORIGIN: "*"
      RABBITMQ_URL: amqp://atma_user:atma_password@rabbitmq:5672
      EVENTS_EXCHANGE_NAME: atma_events_exchange
      EVENTS_QUEUE_NAME_ARCHIVE: analysis_events_archive
      CONSUMER_PREFETCH: 10
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./archive-service/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped

  # Assessment Service
  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    container_name: atma-assessment-service
    environment:
      PORT: 3003
      NODE_ENV: production
      JWT_SECRET: atma_super_secret_jwt_key_production_2024
      RABBITMQ_URL: amqp://atma_user:atma_password@rabbitmq:5672
      RABBITMQ_USER: atma_user
      RABBITMQ_PASSWORD: atma_password
      QUEUE_NAME: assessment_analysis
      EXCHANGE_NAME: atma_exchange
      ROUTING_KEY: analysis.process
      AUTH_SERVICE_URL: http://auth-service:3001
      ARCHIVE_SERVICE_URL: http://archive-service:3002
      ANALYSIS_TOKEN_COST: 1
      QUEUE_DURABLE: true
      MESSAGE_PERSISTENT: true
      EVENTS_EXCHANGE_NAME: atma_events_exchange
      EVENTS_QUEUE_NAME_ASSESSMENTS: analysis_events_assessments
      CONSUMER_PREFETCH: 10
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: atma_db
      DB_USER: atma_user
      DB_PASSWORD: atma_password
      DB_DIALECT: postgres
      DB_SCHEMA: assessment
      DB_POOL_MAX: 25
      DB_POOL_MIN: 5
      DB_POOL_ACQUIRE: 30000
      DB_POOL_IDLE: 20000
      DB_POOL_EVICT: 5000
      IDEMPOTENCY_ENABLED: true
      IDEMPOTENCY_TTL_HOURS: 24
      IDEMPOTENCY_MAX_CACHE_SIZE: 10000
      IDEMPOTENCY_CLEANUP_INTERVAL_MINUTES: 60
      LOG_LEVEL: info
      LOG_FILE: logs/assessment-service.log
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
    ports:
      - "3003:3003"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      auth-service:
        condition: service_healthy
    volumes:
      - ./assessment-service/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped

  # Notification Service
  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: atma-notification-service
    environment:
      PORT: 3005
      NODE_ENV: production
      JWT_SECRET: atma_super_secret_jwt_key_production_2024
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
      CORS_ORIGIN: "*"
      LOG_LEVEL: info
      LOG_FILE: logs/notification-service.log
      SOCKET_PING_TIMEOUT: 60000
      SOCKET_PING_INTERVAL: 25000
      RABBITMQ_URL: amqp://atma_user:atma_password@rabbitmq:5672
      EVENTS_EXCHANGE_NAME: atma_events_exchange
      EVENTS_QUEUE_NAME_NOTIFICATIONS: analysis_events_notifications
      CONSUMER_PREFETCH: 10
    ports:
      - "3005:3005"
    depends_on:
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./notification-service/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped

  # Analysis Worker
  analysis-worker:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker
    environment:
      NODE_ENV: production
      RABBITMQ_URL: amqp://atma_user:atma_password@rabbitmq:5672
      QUEUE_NAME: assessment_analysis
      EXCHANGE_NAME: atma_exchange
      ROUTING_KEY: analysis.process
      WORKER_CONCURRENCY: 10
      WORKER_INSTANCE_ID: worker-1
      LOG_LEVEL: info
      LOG_FILE: logs/analysis-worker.log
      GOOGLE_AI_API_KEY: your_google_ai_api_key_here
      AI_MODEL: gemini-1.5-flash
      MAX_TOKENS: 8192
      TEMPERATURE: 0.7
      TOP_P: 0.8
      TOP_K: 40
      SAFETY_THRESHOLD: BLOCK_MEDIUM_AND_ABOVE
      ENABLE_TOKEN_COUNT_FALLBACK: true
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
      ARCHIVE_SERVICE_URL: http://archive-service:3002
      ARCHIVE_TIMEOUT: 30000
      ARCHIVE_RETRY_ATTEMPTS: 3
      ARCHIVE_RETRY_DELAY: 1000
      CIRCUIT_BREAKER_THRESHOLD: 5
      CIRCUIT_BREAKER_TIMEOUT: 60000
      ENABLE_BATCH_PROCESSING: true
      ARCHIVE_BATCH_SIZE: 10
      ARCHIVE_BATCH_INTERVAL: 5000
      EVENTS_EXCHANGE_NAME: atma_events_exchange
      EVENTS_QUEUE_NAME_WORKERS: analysis_events_workers
      CONSUMER_PREFETCH: 10
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_healthy
    volumes:
      - ./analysis-worker/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: atma-api-gateway
    environment:
      PORT: 3000
      NODE_ENV: production
      AUTH_SERVICE_URL: http://auth-service:3001
      ARCHIVE_SERVICE_URL: http://archive-service:3002
      ASSESSMENT_SERVICE_URL: http://assessment-service:3003
      NOTIFICATION_SERVICE_URL: http://notification-service:3005
      JWT_SECRET: atma_super_secret_jwt_key_production_2024
      INTERNAL_SERVICE_KEY: internal_service_secret_production_2024
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 5000
      ALLOWED_ORIGINS: "*"
      LOG_LEVEL: info
      LOG_FORMAT: combined
      HEALTH_CHECK_INTERVAL: 30000
      SERVICE_TIMEOUT: 5000
    ports:
      - "3000:3000"
    depends_on:
      auth-service:
        condition: service_healthy
      archive-service:
        condition: service_healthy
      assessment-service:
        condition: service_healthy
      notification-service:
        condition: service_healthy
    volumes:
      - ./api-gateway/logs:/app/logs
    networks:
      - atma-network
    restart: unless-stopped

volumes:
  postgres_data:
  rabbitmq_data:

networks:
  atma-network:
    driver: bridge
